<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c4818e3f-a8b5-49f4-88d2-cf0e6c309a59" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 3
}]]></component>
  <component name="ProjectId" id="32idVw9q8wcNW2p3sOtZZc5qJ55" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.CustomerSuccessAnalyticsApplication.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "/home/<USER>/repo/sd-customer-success"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="CustomerSuccessAnalyticsApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.kylas.analytics.CustomerSuccessAnalyticsApplication" />
      <module name="customer-success-analytics" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.kylas.analytics.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="sd-customer-success" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="sd-customer-success" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.CustomerSuccessAnalyticsApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c4818e3f-a8b5-49f4-88d2-cf0e6c309a59" name="Changes" comment="" />
      <created>1757907353819</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1757907353819</updated>
    </task>
    <servers />
  </component>
  <component name="ai.zencoder.plugin.mcp">
    <option name="internalToolsState" value="{&quot;file_search&quot;:true,&quot;list_resources&quot;:true,&quot;fulltext_search&quot;:true,&quot;read_resource&quot;:true}" />
  </component>
</project>