<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::title}, ~{::#content})}">
<head>
    <title>Dashboard - Customer Success Analytics</title>
</head>
<body>
    <div id="content">
        <!-- Key Metrics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card metric-card primary">
                    <div class="card-body text-center">
                        <i class="fas fa-building fa-2x mb-2"></i>
                        <h3 th:text="${metrics.totalTenants}">0</h3>
                        <p class="mb-0">Total Tenants</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card success">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h3 th:text="${metrics.activeTenants}">0</h3>
                        <p class="mb-0">Active Tenants</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card warning">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h3 th:text="${metrics.totalUsers}">0</h3>
                        <p class="mb-0">Total Users</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card info">
                    <div class="card-body text-center">
                        <i class="fas fa-user-check fa-2x mb-2"></i>
                        <h3 th:text="${metrics.dailyActiveUsers}">0</h3>
                        <p class="mb-0">Daily Active Users</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Engagement Metrics -->
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>
                            User Engagement Rate
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="text-primary" th:text="${#numbers.formatDecimal(metrics.userEngagementRate, 1, 1)} + '%'">0%</h3>
                                <p class="text-muted mb-0">Daily Active Users / Total Users</p>
                            </div>
                            <div class="text-end">
                                <i class="fas fa-chart-line fa-3x text-primary opacity-25"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-heart me-2"></i>
                            Tenant Retention Rate
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="text-success" th:text="${#numbers.formatDecimal(metrics.tenantRetentionRate, 1, 1)} + '%'">0%</h3>
                                <p class="text-muted mb-0">Active Tenants / Total Tenants</p>
                            </div>
                            <div class="text-end">
                                <i class="fas fa-heart fa-3x text-success opacity-25"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Tenant Signup Trend (Last 30 Days)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="tenantTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>
                            User Activity Trend (Last 30 Days)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="userActivityChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Distribution Charts -->
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-industry me-2"></i>
                            Industry Distribution
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="industryChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tags me-2"></i>
                            Plan Distribution
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="planChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performers -->
        <div class="row">
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy me-2"></i>
                            Top Tenants by Activity
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Tenant</th>
                                        <th>Industry</th>
                                        <th>Activity</th>
                                        <th>Users</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="tenant, iterStat : ${metrics.topTenantsByActivity}" th:if="${iterStat.index < 5}">
                                        <td>
                                            <strong th:text="${tenant.tenantName}">Tenant Name</strong>
                                            <br>
                                            <small class="text-muted" th:text="${tenant.tenantId}">ID</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary" th:text="${tenant.industry ?: 'Unknown'}">Industry</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary" th:text="${#numbers.formatInteger(tenant.totalActivity, 0, 'COMMA')}">0</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success" th:text="${tenant.activeUsers}">0</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star me-2"></i>
                            Top Users by Activity
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Tenant</th>
                                        <th>Activity</th>
                                        <th>DAU</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="user, iterStat : ${metrics.topUsersByActivity}" th:if="${iterStat.index < 5}">
                                        <td>
                                            <strong th:text="${user.fullName}">User Name</strong>
                                            <br>
                                            <small class="text-muted" th:text="${user.email}">email</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info" th:text="${user.tenantName}">Tenant</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary" th:text="${user.totalActivity}">0</span>
                                        </td>
                                        <td>
                                            <span th:if="${user.DAU}" class="badge bg-success">
                                                <i class="fas fa-check"></i>
                                            </span>
                                            <span th:unless="${user.DAU}" class="badge bg-secondary">
                                                <i class="fas fa-times"></i>
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chart Scripts -->
        <script th:inline="javascript">
            // Tenant Signup Trend Chart
            const tenantTrendData = /*[[${metrics.tenantSignupTrend}]]*/ [];
            const tenantTrendCtx = document.getElementById('tenantTrendChart').getContext('2d');
            new Chart(tenantTrendCtx, {
                type: 'line',
                data: {
                    labels: tenantTrendData.map(item => item.date),
                    datasets: [{
                        label: 'New Signups',
                        data: tenantTrendData.map(item => item.count),
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // User Activity Trend Chart
            const userActivityData = /*[[${metrics.userActivityTrend}]]*/ [];
            const userActivityCtx = document.getElementById('userActivityChart').getContext('2d');
            new Chart(userActivityCtx, {
                type: 'line',
                data: {
                    labels: userActivityData.map(item => item.date),
                    datasets: [{
                        label: 'Daily Active Users',
                        data: userActivityData.map(item => item.count),
                        borderColor: '#764ba2',
                        backgroundColor: 'rgba(118, 75, 162, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Industry Distribution Chart
            const industryData = /*[[${metrics.industryDistribution}]]*/ {};
            const industryCtx = document.getElementById('industryChart').getContext('2d');
            new Chart(industryCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(industryData),
                    datasets: [{
                        data: Object.values(industryData),
                        backgroundColor: [
                            '#667eea', '#764ba2', '#f093fb', '#f5576c',
                            '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Plan Distribution Chart
            const planData = /*[[${metrics.planDistribution}]]*/ {};
            const planCtx = document.getElementById('planChart').getContext('2d');
            new Chart(planCtx, {
                type: 'pie',
                data: {
                    labels: Object.keys(planData),
                    datasets: [{
                        data: Object.values(planData),
                        backgroundColor: [
                            '#667eea', '#764ba2', '#f093fb', '#f5576c',
                            '#4facfe', '#00f2fe'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        </script>
    </div>
</body>
</html>
