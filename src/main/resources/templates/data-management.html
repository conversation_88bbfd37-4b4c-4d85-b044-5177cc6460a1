<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout :: layout(~{::title}, ~{::#content})}">
<head>
    <title>Data Management - Customer Success Analytics</title>
</head>
<body>
    <div id="content">
        <!-- Current Data Status -->
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <div class="card metric-card primary">
                    <div class="card-body text-center">
                        <i class="fas fa-building fa-2x mb-2"></i>
                        <h3 th:text="${tenantCount}">0</h3>
                        <p class="mb-0">Tenant Records</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card metric-card success">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h3 th:text="${userCount}">0</h3>
                        <p class="mb-0">User Records</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Load Initial Data -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-download me-2"></i>
                            Load Initial CSV Data
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">
                            Load the initial CSV files (onboarding-test.csv and usage-test.csv) from the workspace.
                        </p>
                        <form th:action="@{/data/load-initial}" method="post" class="d-inline">
                            <button type="submit" class="btn btn-primary" onclick="return confirm('This will load data from the workspace CSV files. Continue?')">
                                <i class="fas fa-play me-2"></i>
                                Load Initial Data
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload CSV Files -->
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-upload me-2"></i>
                            Upload Tenant Onboarding CSV
                        </h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/data/upload-tenants}" method="post" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="tenantFile" class="form-label">Select CSV File</label>
                                <input type="file" class="form-control" id="tenantFile" name="file" accept=".csv" required>
                                <div class="form-text">
                                    Upload a CSV file containing tenant onboarding data.
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-2"></i>
                                Upload Tenant Data
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-upload me-2"></i>
                            Upload User Usage CSV
                        </h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/data/upload-users}" method="post" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="userFile" class="form-label">Select CSV File</label>
                                <input type="file" class="form-control" id="userFile" name="file" accept=".csv" required>
                                <div class="form-text">
                                    Upload a CSV file containing user usage data.
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-2"></i>
                                Upload User Data
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Management Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            Data Management Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="d-grid">
                                    <form th:action="@{/data/clear}" method="post" class="d-inline">
                                        <button type="submit" class="btn btn-danger" onclick="return confirm('This will delete ALL data. Are you sure?')">
                                            <i class="fas fa-trash me-2"></i>
                                            Clear All Data
                                        </button>
                                    </form>
                                </div>
                                <small class="text-muted">
                                    Remove all tenant and user data from the database.
                                </small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-grid">
                                    <a href="/dashboard" class="btn btn-success">
                                        <i class="fas fa-chart-line me-2"></i>
                                        View Analytics Dashboard
                                    </a>
                                </div>
                                <small class="text-muted">
                                    Go to the main analytics dashboard to view reports.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CSV Format Information -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            CSV Format Requirements
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Tenant Onboarding CSV</h6>
                                <p class="text-muted">Required columns:</p>
                                <ul class="list-unstyled">
                                    <li><code>tenantId</code> - Unique tenant identifier</li>
                                    <li><code>tenantName</code> - Name of the tenant</li>
                                    <li><code>tenantIndustry</code> - Industry category</li>
                                    <li><code>signedUpAt</code> - Signup date (ISO format)</li>
                                    <li><code>tenantUserEmail</code> - Primary contact email</li>
                                    <li>... and other metrics columns</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>User Usage CSV</h6>
                                <p class="text-muted">Required columns:</p>
                                <ul class="list-unstyled">
                                    <li><code>userId</code> - Unique user identifier</li>
                                    <li><code>fullName</code> - User's full name</li>
                                    <li><code>email</code> - User's email address</li>
                                    <li><code>tenantId</code> - Associated tenant ID</li>
                                    <li><code>usagePublishedDate</code> - Usage date (ISO format)</li>
                                    <li>... and other activity columns</li>
                                </ul>
                            </div>
                        </div>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>Tip:</strong> Ensure your CSV files have headers and use comma separation. 
                            Date fields should be in ISO format (YYYY-MM-DDTHH:mm:ss.sssZ).
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
