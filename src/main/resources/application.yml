spring:
  application:
    name: customer-success-analytics
  
  datasource:
    url: *****************************************
    username: postgres
    password: password
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 30000
      pool-name: SpringBootJPAHikariCP
      max-lifetime: 2000000
      connection-timeout: 30000

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        use_sql_comments: true
        hbm2ddl:
          auto: create-drop
        globally_quoted_identifiers: false
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false

  h2:
    console:
      enabled: true

  thymeleaf:
    cache: false
    prefix: classpath:/templates/
    suffix: .html
    encoding: UTF-8
    mode: HTML

  security:
    user:
      name: <EMAIL>
      password: test@123

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

  cache:
    type: simple
    cache-names:
      - dashboardMetrics
      - dauTrend
      - tenantTrend
      - engagementMetrics
      - topUsers
      - topTenants
      - appUsage
      - retentionMetrics
      - aggregatedMetrics
      - tenantUsageTrends

server:
  port: 8080
  servlet:
    context-path: /
    session:
      timeout: 30m

logging:
  level:
    com.kylas: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Application specific properties
app:
  csv:
    upload-dir: ${java.io.tmpdir}/csv-uploads
    batch-size: 1000
    max-file-size: 10MB
    allowed-extensions: csv
  analytics:
    cache-duration: 3600 # seconds
    default-date-range: 30 # days
  security:
    session-timeout: 1800 # seconds
  data:
    archive:
      retention-days: 90
      batch-size: 1000
      history-retention-days: 365
      enabled: true
      schedule: "0 0 2 * * ?" # Daily at 2 AM
  external:
    api:
      base-url: http://localhost:8080
      timeout: 30s
      retry:
        max-attempts: 3
        delay: 1s

# Management and monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
