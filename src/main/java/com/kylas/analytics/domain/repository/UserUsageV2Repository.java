package com.kylas.analytics.domain.repository;

import com.kylas.analytics.domain.entity.UserUsageV2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository for UserUsageV2 entity.
 * Follows Interface Segregation Principle by providing focused query methods.
 */
@Repository
public interface UserUsageV2Repository extends JpaRepository<UserUsageV2, String> {

    /**
     * Find user usage records by tenant ID
     */
    List<UserUsageV2> findByTenantId(String tenantId);

    /**
     * Find user usage records by tenant ID with pagination
     */
    Page<UserUsageV2> findByTenantId(String tenantId, Pageable pageable);

    /**
     * Find user usage records by email
     */
    List<UserUsageV2> findByEmail(String email);

    /**
     * Find user usage records within date range
     */
    @Query("SELECT u FROM UserUsageV2 u WHERE u.usagePublishedDate BETWEEN :startDate AND :endDate")
    List<UserUsageV2> findByUsagePublishedDateBetween(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate
    );

    /**
     * Find user usage records by tenant and date range
     */
    @Query("SELECT u FROM UserUsageV2 u WHERE u.tenantId = :tenantId " +
           "AND u.usagePublishedDate BETWEEN :startDate AND :endDate")
    List<UserUsageV2> findByTenantIdAndUsagePublishedDateBetween(
        @Param("tenantId") String tenantId,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate
    );

    /**
     * Find latest usage record for a user
     */
    @Query("SELECT u FROM UserUsageV2 u WHERE u.userId = :userId " +
           "ORDER BY u.usagePublishedDate DESC")
    Optional<UserUsageV2> findLatestByUserId(@Param("userId") String userId);

    /**
     * Count total users
     */
    @Query("SELECT COUNT(DISTINCT u.userId) FROM UserUsageV2 u")
    long countDistinctUsers();

    /**
     * Count users by tenant
     */
    @Query("SELECT COUNT(DISTINCT u.userId) FROM UserUsageV2 u WHERE u.tenantId = :tenantId")
    long countDistinctUsersByTenantId(@Param("tenantId") String tenantId);

    /**
     * Find users by status
     */
    List<UserUsageV2> findByStatus(String status);

    /**
     * Search users by name or email
     */
    @Query("SELECT u FROM UserUsageV2 u WHERE " +
           "LOWER(u.fullName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<UserUsageV2> searchUsers(@Param("searchTerm") String searchTerm);

    /**
     * Find records older than specified date for archiving
     */
    @Query("SELECT u FROM UserUsageV2 u WHERE u.usagePublishedDate < :cutoffDate")
    List<UserUsageV2> findRecordsForArchiving(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Delete records older than specified date
     */
    @Modifying
    @Query("DELETE FROM UserUsageV2 u WHERE u.usagePublishedDate < :cutoffDate")
    int deleteRecordsOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Find records by tenant and plan
     */
    @Query("SELECT u FROM UserUsageV2 u WHERE u.tenantId = :tenantId AND u.planName = :planName")
    List<UserUsageV2> findByTenantIdAndPlanName(
        @Param("tenantId") String tenantId, 
        @Param("planName") String planName
    );

    /**
     * Get user count by plan
     */
    @Query("SELECT u.planName, COUNT(DISTINCT u.userId) FROM UserUsageV2 u " +
           "WHERE u.planName IS NOT NULL GROUP BY u.planName")
    List<Object[]> getUserCountByPlan();

    /**
     * Get user count by tenant
     */
    @Query("SELECT u.tenantId, u.tenantName, COUNT(DISTINCT u.userId) FROM UserUsageV2 u " +
           "GROUP BY u.tenantId, u.tenantName ORDER BY COUNT(DISTINCT u.userId) DESC")
    List<Object[]> getUserCountByTenant();

    /**
     * Find users with phone numbers
     */
    @Query("SELECT u FROM UserUsageV2 u WHERE u.primaryPhoneNumber IS NOT NULL")
    List<UserUsageV2> findUsersWithPhoneNumbers();

    /**
     * Get latest usage date for each user
     */
    @Query("SELECT u.userId, MAX(u.usagePublishedDate) FROM UserUsageV2 u GROUP BY u.userId")
    List<Object[]> getLatestUsageDateByUser();

    /**
     * Find duplicate records (same user, same date)
     */
    @Query("SELECT u.userId, u.usagePublishedDate, COUNT(*) FROM UserUsageV2 u " +
           "GROUP BY u.userId, u.usagePublishedDate HAVING COUNT(*) > 1")
    List<Object[]> findDuplicateRecords();

    /**
     * Check if user exists
     */
    boolean existsByUserId(String userId);

    /**
     * Check if user exists for tenant
     */
    boolean existsByUserIdAndTenantId(String userId, String tenantId);

    /**
     * Find records updated after specific date
     */
    @Query("SELECT u FROM UserUsageV2 u WHERE u.recordUpdatedAt > :lastSyncDate")
    List<UserUsageV2> findRecordsUpdatedAfter(@Param("lastSyncDate") LocalDateTime lastSyncDate);

    /**
     * Get usage statistics by date range
     */
    @Query("SELECT CAST(u.usagePublishedDate AS DATE) as usageDate, COUNT(*) as recordCount, " +
           "COUNT(DISTINCT u.userId) as uniqueUsers, COUNT(DISTINCT u.tenantId) as uniqueTenants " +
           "FROM UserUsageV2 u WHERE u.usagePublishedDate BETWEEN :startDate AND :endDate " +
           "GROUP BY CAST(u.usagePublishedDate AS DATE) ORDER BY usageDate")
    List<Object[]> getUsageStatisticsByDateRange(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate
    );
}
