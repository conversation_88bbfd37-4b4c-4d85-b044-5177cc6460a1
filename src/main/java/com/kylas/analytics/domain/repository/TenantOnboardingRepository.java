package com.kylas.analytics.domain.repository;

import com.kylas.analytics.domain.entity.TenantOnboarding;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for TenantOnboarding entity.
 * Provides data access methods for tenant onboarding analytics.
 */
@Repository
public interface TenantOnboardingRepository extends JpaRepository<TenantOnboarding, String> {

    /**
     * Find tenants by status
     */
    List<TenantOnboarding> findByStatus(String status);

    /**
     * Find tenants by plan name
     */
    List<TenantOnboarding> findByPlanName(String planName);

    /**
     * Find tenants by industry
     */
    List<TenantOnboarding> findByTenantIndustry(String industry);

    /**
     * Find tenants signed up within date range
     */
    List<TenantOnboarding> findBySignedUpAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find tenants with account settings completed
     */
    List<TenantOnboarding> findByAccountSettingsCompleted(Boolean completed);

    /**
     * Find active tenants with pagination
     */
    Page<TenantOnboarding> findByStatus(String status, Pageable pageable);

    /**
     * Count tenants by status
     */
    @Query("SELECT COUNT(t) FROM TenantOnboarding t WHERE t.status = :status")
    Long countByStatus(@Param("status") String status);

    /**
     * Count tenants by plan name
     */
    @Query("SELECT COUNT(t) FROM TenantOnboarding t WHERE t.planName = :planName")
    Long countByPlanName(@Param("planName") String planName);

    /**
     * Get tenant signup trends by date range
     */
    @Query("SELECT CAST(t.signedUpAt AS DATE) as signupDate, COUNT(t) as count " +
           "FROM TenantOnboarding t " +
           "WHERE t.signedUpAt BETWEEN :startDate AND :endDate " +
           "GROUP BY CAST(t.signedUpAt AS DATE) " +
           "ORDER BY signupDate")
    List<Object[]> getTenantSignupTrends(@Param("startDate") LocalDateTime startDate, 
                                        @Param("endDate") LocalDateTime endDate);

    /**
     * Get industry distribution
     */
    @Query("SELECT t.tenantIndustry, COUNT(t) as count " +
           "FROM TenantOnboarding t " +
           "WHERE t.tenantIndustry IS NOT NULL " +
           "GROUP BY t.tenantIndustry " +
           "ORDER BY count DESC")
    List<Object[]> getIndustryDistribution();

    /**
     * Get plan distribution
     */
    @Query("SELECT t.planName, COUNT(t) as count " +
           "FROM TenantOnboarding t " +
           "WHERE t.planName IS NOT NULL " +
           "GROUP BY t.planName " +
           "ORDER BY count DESC")
    List<Object[]> getPlanDistribution();

    /**
     * Get average user counts by plan
     */
    @Query("SELECT t.planName, " +
           "AVG(t.activeUserCount) as avgActiveUsers, " +
           "AVG(t.inactiveUserCount) as avgInactiveUsers " +
           "FROM TenantOnboarding t " +
           "WHERE t.planName IS NOT NULL " +
           "GROUP BY t.planName")
    List<Object[]> getAverageUserCountsByPlan();

    /**
     * Get top tenants by activity metrics
     */
    @Query("SELECT t FROM TenantOnboarding t " +
           "WHERE t.status = 'active' " +
           "ORDER BY (t.leadCount + t.dealCount + t.contactCount) DESC")
    List<TenantOnboarding> getTopTenantsByActivity(Pageable pageable);

    /**
     * Get marketplace app adoption rates
     */
    @Query("SELECT t.numberOfMarketplaceAppsInstalled, COUNT(t) as count " +
           "FROM TenantOnboarding t " +
           "WHERE t.numberOfMarketplaceAppsInstalled IS NOT NULL " +
           "GROUP BY t.numberOfMarketplaceAppsInstalled " +
           "ORDER BY t.numberOfMarketplaceAppsInstalled")
    List<Object[]> getMarketplaceAppAdoptionRates();

    /**
     * Find tenants with high engagement (DAU > threshold)
     */
    @Query("SELECT t FROM TenantOnboarding t " +
           "WHERE t.dauTrue > :threshold " +
           "ORDER BY t.dauTrue DESC")
    List<TenantOnboarding> findHighEngagementTenants(@Param("threshold") Integer threshold);

    /**
     * Get onboarding completion metrics
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN t.accountSettingsCompleted = true THEN 1 END) as completed, " +
           "COUNT(CASE WHEN t.accountSettingsCompleted = false THEN 1 END) as incomplete, " +
           "COUNT(t) as total " +
           "FROM TenantOnboarding t")
    Object[] getOnboardingCompletionMetrics();

    /**
     * Find tenant by email
     */
    Optional<TenantOnboarding> findByTenantUserEmail(String email);

    /**
     * Search tenants by name (case insensitive)
     */
    @Query("SELECT t FROM TenantOnboarding t " +
           "WHERE LOWER(t.tenantName) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<TenantOnboarding> searchByTenantName(@Param("name") String name);
}
