{"snapshots": {"/home/<USER>/repo/sd-customer-success/src/main/resources/application.yml": {"filePath": "/home/<USER>/repo/sd-customer-success/src/main/resources/application.yml", "baseContent": "spring:\n  application:\n    name: customer-success-analytics\n  \n  datasource:\n    url: *******************************************    username: postgres\n    password: password\n    hikari:\n      maximum-pool-size: 10\n      minimum-idle: 5\n      idle-timeout: 30000\n      pool-name: SpringBootJPAHikariCP\n      max-lifetime: 2000000\n      connection-timeout: 30000\n\n  jpa:\n    hibernate:\n      ddl-auto: create-drop\n    show-sql: true\n    properties:\n      hibernate:\n        dialect: org.hibernate.dialect.H2Dialect\n        format_sql: true\n        use_sql_comments: true\n        hbm2ddl:\n          auto: create-drop\n        globally_quoted_identifiers: false\n        jdbc:\n          batch_size: 20\n        order_inserts: true\n        order_updates: true\n    open-in-view: false\n\n  h2:\n    console:\n      enabled: true\n\n  thymeleaf:\n    cache: false\n    prefix: classpath:/templates/\n    suffix: .html\n    encoding: UTF-8\n    mode: HTML\n\n  security:\n    user:\n      name: <EMAIL>\n      password: test@123\n\n  servlet:\n    multipart:\n      max-file-size: 10MB\n      max-request-size: 10MB\n\n  cache:\n    type: simple\n    cache-names:\n      - dashboardMetrics\n      - dauTrend\n      - tenantTrend\n      - engagementMetrics\n      - topUsers\n      - topTenants\n      - appUsage\n      - retentionMetrics\n      - aggregatedMetrics\n      - tenantUsageTrends\n\nserver:\n  port: 8080\n  servlet:\n    context-path: /\n    session:\n      timeout: 30m\n\nlogging:\n  level:\n    com.kylas: DEBUG\n    org.springframework.security: DEBUG\n    org.hibernate.SQL: DEBUG\n    org.hibernate.type.descriptor.sql.BasicBinder: TRACE\n  pattern:\n    console: \"%d{yyyy-MM-dd HH:mm:ss} - %msg%n\"\n    file: \"%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n\"\n\n# Application specific properties\napp:\n  csv:\n    upload-dir: ${java.io.tmpdir}/csv-uploads\n    batch-size: 1000\n    max-file-size: 10MB\n    allowed-extensions: csv\n  analytics:\n    cache-duration: 3600 # seconds\n    default-date-range: 30 # days\n  security:\n    session-timeout: 1800 # seconds\n  data:\n    archive:\n      retention-days: 90\n      batch-size: 1000\n      history-retention-days: 365\n      enabled: true\n      schedule: \"0 0 2 * * ?\" # Daily at 2 AM\n  external:\n    api:\n      base-url: http://localhost:8080\n      timeout: 30s\n      retry:\n        max-attempts: 3\n        delay: 1s\n\n# Management and monitoring\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: health,info,metrics,prometheus\n  endpoint:\n    health:\n      show-details: always\n  metrics:\n    export:\n      prometheus:\n        enabled: true\n", "baseTimestamp": 1757907761351}, "/home/<USER>/repo/sd-customer-success/src/main/resources/templates/dashboard.html": {"filePath": "/home/<USER>/repo/sd-customer-success/src/main/resources/templates/dashboard.html", "baseContent": "<!DOCTYPE html>\n<html lang=\"en\" xmlns:th=\"http://www.thymeleaf.org\" th:replace=\"~{layout :: layout(~{::title}, ~{::#content})}\">\n<head>\n    <title>Dashboard - Customer Success Analytics</title>\n</head>\n<body>\n    <div id=\"content\">\n        <!-- Key Metrics Cards -->\n        <div class=\"row mb-4\">\n            <div class=\"col-md-3 mb-3\">\n                <div class=\"card metric-card primary\">\n                    <div class=\"card-body text-center\">\n                        <i class=\"fas fa-building fa-2x mb-2\"></i>\n                        <h3 th:text=\"${metrics.totalTenants}\">0</h3>\n                        <p class=\"mb-0\">Total Tenants</p>\n                    </div>\n                </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n                <div class=\"card metric-card success\">\n                    <div class=\"card-body text-center\">\n                        <i class=\"fas fa-check-circle fa-2x mb-2\"></i>\n                        <h3 th:text=\"${metrics.activeTenants}\">0</h3>\n                        <p class=\"mb-0\">Active Tenants</p>\n                    </div>\n                </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n                <div class=\"card metric-card warning\">\n                    <div class=\"card-body text-center\">\n                        <i class=\"fas fa-users fa-2x mb-2\"></i>\n                        <h3 th:text=\"${metrics.totalUsers}\">0</h3>\n                        <p class=\"mb-0\">Total Users</p>\n                    </div>\n                </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n                <div class=\"card metric-card info\">\n                    <div class=\"card-body text-center\">\n                        <i class=\"fas fa-user-check fa-2x mb-2\"></i>\n                        <h3 th:text=\"${metrics.dailyActiveUsers}\">0</h3>\n                        <p class=\"mb-0\">Daily Active Users</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- Engagement Metrics -->\n        <div class=\"row mb-4\">\n            <div class=\"col-md-6 mb-3\">\n                <div class=\"card\">\n                    <div class=\"card-header\">\n                        <h5 class=\"mb-0\">\n                            <i class=\"fas fa-chart-pie me-2\"></i>\n                            User Engagement Rate\n                        </h5>\n                    </div>\n                    <div class=\"card-body\">\n                        <div class=\"d-flex justify-content-between align-items-center\">\n                            <div>\n                                <h3 class=\"text-primary\" th:text=\"${#numbers.formatDecimal(metrics.userEngagementRate, 1, 1)} + '%'\">0%</h3>\n                                <p class=\"text-muted mb-0\">Daily Active Users / Total Users</p>\n                            </div>\n                            <div class=\"text-end\">\n                                <i class=\"fas fa-chart-line fa-3x text-primary opacity-25\"></i>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <div class=\"col-md-6 mb-3\">\n                <div class=\"card\">\n                    <div class=\"card-header\">\n                        <h5 class=\"mb-0\">\n                            <i class=\"fas fa-heart me-2\"></i>\n                            Tenant Retention Rate\n                        </h5>\n                    </div>\n                    <div class=\"card-body\">\n                        <div class=\"d-flex justify-content-between align-items-center\">\n                            <div>\n                                <h3 class=\"text-success\" th:text=\"${#numbers.formatDecimal(metrics.tenantRetentionRate, 1, 1)} + '%'\">0%</h3>\n                                <p class=\"text-muted mb-0\">Active Tenants / Total Tenants</p>\n                            </div>\n                            <div class=\"text-end\">\n                                <i class=\"fas fa-heart fa-3x text-success opacity-25\"></i>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- Charts Row -->\n        <div class=\"row mb-4\">\n            <div class=\"col-md-6 mb-3\">\n                <div class=\"card\">\n                    <div class=\"card-header\">\n                        <h5 class=\"mb-0\">\n                            <i class=\"fas fa-chart-line me-2\"></i>\n                            Tenant Signup Trend (Last 30 Days)\n                        </h5>\n                    </div>\n                    <div class=\"card-body\">\n                        <div class=\"chart-container\">\n                            <canvas id=\"tenantTrendChart\"></canvas>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <div class=\"col-md-6 mb-3\">\n                <div class=\"card\">\n                    <div class=\"card-header\">\n                        <h5 class=\"mb-0\">\n                            <i class=\"fas fa-chart-area me-2\"></i>\n                            User Activity Trend (Last 30 Days)\n                        </h5>\n                    </div>\n                    <div class=\"card-body\">\n                        <div class=\"chart-container\">\n                            <canvas id=\"userActivityChart\"></canvas>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- Distribution Charts -->\n        <div class=\"row mb-4\">\n            <div class=\"col-md-6 mb-3\">\n                <div class=\"card\">\n                    <div class=\"card-header\">\n                        <h5 class=\"mb-0\">\n                            <i class=\"fas fa-industry me-2\"></i>\n                            Industry Distribution\n                        </h5>\n                    </div>\n                    <div class=\"card-body\">\n                        <div class=\"chart-container\">\n                            <canvas id=\"industryChart\"></canvas>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <div class=\"col-md-6 mb-3\">\n                <div class=\"card\">\n                    <div class=\"card-header\">\n                        <h5 class=\"mb-0\">\n                            <i class=\"fas fa-tags me-2\"></i>\n                            Plan Distribution\n                        </h5>\n                    </div>\n                    <div class=\"card-body\">\n                        <div class=\"chart-container\">\n                            <canvas id=\"planChart\"></canvas>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- Top Performers -->\n        <div class=\"row\">\n            <div class=\"col-md-6 mb-3\">\n                <div class=\"card\">\n                    <div class=\"card-header\">\n                        <h5 class=\"mb-0\">\n                            <i class=\"fas fa-trophy me-2\"></i>\n                            Top Tenants by Activity\n                        </h5>\n                    </div>\n                    <div class=\"card-body\">\n                        <div class=\"table-responsive\">\n                            <table class=\"table table-hover\">\n                                <thead>\n                                    <tr>\n                                        <th>Tenant</th>\n                                        <th>Industry</th>\n                                        <th>Activity</th>\n                                        <th>Users</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    <tr th:each=\"tenant, iterStat : ${metrics.topTenantsByActivity}\" th:if=\"${iterStat.index < 5}\">\n                                        <td>\n                                            <strong th:text=\"${tenant.tenantName}\">Tenant Name</strong>\n                                            <br>\n                                            <small class=\"text-muted\" th:text=\"${tenant.tenantId}\">ID</small>\n                                        </td>\n                                        <td>\n                                            <span class=\"badge bg-secondary\" th:text=\"${tenant.industry ?: 'Unknown'}\">Industry</span>\n                                        </td>\n                                        <td>\n                                            <span class=\"badge bg-primary\" th:text=\"${#numbers.formatInteger(tenant.totalActivity, 0, 'COMMA')}\">0</span>\n                                        </td>\n                                        <td>\n                                            <span class=\"badge bg-success\" th:text=\"${tenant.activeUsers}\">0</span>\n                                        </td>\n                                    </tr>\n                                </tbody>\n                            </table>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <div class=\"col-md-6 mb-3\">\n                <div class=\"card\">\n                    <div class=\"card-header\">\n                        <h5 class=\"mb-0\">\n                            <i class=\"fas fa-star me-2\"></i>\n                            Top Users by Activity\n                        </h5>\n                    </div>\n                    <div class=\"card-body\">\n                        <div class=\"table-responsive\">\n                            <table class=\"table table-hover\">\n                                <thead>\n                                    <tr>\n                                        <th>User</th>\n                                        <th>Tenant</th>\n                                        <th>Activity</th>\n                                        <th>DAU</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    <tr th:each=\"user, iterStat : ${metrics.topUsersByActivity}\" th:if=\"${iterStat.index < 5}\">\n                                        <td>\n                                            <strong th:text=\"${user.fullName}\">User Name</strong>\n                                            <br>\n                                            <small class=\"text-muted\" th:text=\"${user.email}\">email</small>\n                                        </td>\n                                        <td>\n                                            <span class=\"badge bg-info\" th:text=\"${user.tenantName}\">Tenant</span>\n                                        </td>\n                                        <td>\n                                            <span class=\"badge bg-primary\" th:text=\"${user.totalActivity}\">0</span>\n                                        </td>\n                                        <td>\n                                            <span th:if=\"${user.DAU}\" class=\"badge bg-success\">\n                                                <i class=\"fas fa-check\"></i>\n                                            </span>\n                                            <span th:unless=\"${user.DAU}\" class=\"badge bg-secondary\">\n                                                <i class=\"fas fa-times\"></i>\n                                            </span>\n                                        </td>\n                                    </tr>\n                                </tbody>\n                            </table>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- Chart Scripts -->\n        <script th:inline=\"javascript\">\n            // Tenant Signup Trend Chart\n            const tenantTrendData = /*[[${metrics.tenantSignupTrend}]]*/ [];\n            const tenantTrendCtx = document.getElementById('tenantTrendChart').getContext('2d');\n            new Chart(tenantTrendCtx, {\n                type: 'line',\n                data: {\n                    labels: tenantTrendData.map(item => item.date),\n                    datasets: [{\n                        label: 'New Signups',\n                        data: tenantTrendData.map(item => item.count),\n                        borderColor: '#667eea',\n                        backgroundColor: 'rgba(102, 126, 234, 0.1)',\n                        tension: 0.4,\n                        fill: true\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {\n                            display: false\n                        }\n                    },\n                    scales: {\n                        y: {\n                            beginAtZero: true\n                        }\n                    }\n                }\n            });\n\n            // User Activity Trend Chart\n            const userActivityData = /*[[${metrics.userActivityTrend}]]*/ [];\n            const userActivityCtx = document.getElementById('userActivityChart').getContext('2d');\n            new Chart(userActivityCtx, {\n                type: 'line',\n                data: {\n                    labels: userActivityData.map(item => item.date),\n                    datasets: [{\n                        label: 'Daily Active Users',\n                        data: userActivityData.map(item => item.count),\n                        borderColor: '#764ba2',\n                        backgroundColor: 'rgba(118, 75, 162, 0.1)',\n                        tension: 0.4,\n                        fill: true\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {\n                            display: false\n                        }\n                    },\n                    scales: {\n                        y: {\n                            beginAtZero: true\n                        }\n                    }\n                }\n            });\n\n            // Industry Distribution Chart\n            const industryData = /*[[${metrics.industryDistribution}]]*/ {};\n            const industryCtx = document.getElementById('industryChart').getContext('2d');\n            new Chart(industryCtx, {\n                type: 'doughnut',\n                data: {\n                    labels: Object.keys(industryData),\n                    datasets: [{\n                        data: Object.values(industryData),\n                        backgroundColor: [\n                            '#667eea', '#764ba2', '#f093fb', '#f5576c',\n                            '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'\n                        ]\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {\n                            position: 'bottom'\n                        }\n                    }\n                }\n            });\n\n            // Plan Distribution Chart\n            const planData = /*[[${metrics.planDistribution}]]*/ {};\n            const planCtx = document.getElementById('planChart').getContext('2d');\n            new Chart(planCtx, {\n                type: 'pie',\n                data: {\n                    labels: Object.keys(planData),\n                    datasets: [{\n                        data: Object.values(planData),\n                        backgroundColor: [\n                            '#667eea', '#764ba2', '#f093fb', '#f5576c',\n                            '#4facfe', '#00f2fe'\n                        ]\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    plugins: {\n                        legend: {\n                            position: 'bottom'\n                        }\n                    }\n                }\n            });\n        </script>\n    </div>\n</body>\n</html>\n", "baseTimestamp": 1757908114750}}}